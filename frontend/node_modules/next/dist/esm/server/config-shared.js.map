{"version": 3, "sources": ["../../src/server/config-shared.ts"], "names": ["os", "imageConfigDefault", "defaultConfig", "env", "webpack", "eslint", "ignoreDuringBuilds", "typescript", "ignoreBuildErrors", "tsconfigPath", "distDir", "cleanDistDir", "assetPrefix", "cache<PERSON><PERSON><PERSON>", "undefined", "cacheMaxMemorySize", "config<PERSON><PERSON><PERSON>", "useFileSystemPublicRoutes", "generateBuildId", "generateEtags", "pageExtensions", "poweredByHeader", "compress", "analyticsId", "process", "VERCEL_ANALYTICS_ID", "images", "devIndicators", "buildActivity", "buildActivityPosition", "onDemandEntries", "maxInactiveAge", "pagesBufferLength", "amp", "canonicalBase", "basePath", "sassOptions", "trailingSlash", "i18n", "productionBrowserSourceMaps", "optimizeFonts", "excludeDefaultMomentLocales", "serverRuntimeConfig", "publicRuntimeConfig", "reactProductionProfiling", "reactStrictMode", "httpAgentOptions", "keepAlive", "outputFileTracing", "staticPageGenerationTimeout", "swcMinify", "output", "NEXT_PRIVATE_STANDALONE", "modularizeImports", "experimental", "multiZoneDraftMode", "prerenderEarlyExit", "serverMinification", "serverSourceMaps", "linkNoTouchStart", "caseSensitiveRoutes", "appDocumentPreloading", "preloadEntriesOnStart", "clientRouterFilter", "clientRouterFilterRedirects", "fetchCacheKeyPrefix", "middlewarePrefetch", "optimisticClientCache", "swr<PERSON><PERSON><PERSON>", "manualClientBasePath", "cpus", "Math", "max", "Number", "CIRCLE_NODE_TOTAL", "length", "memoryBasedWorkersCount", "isrFlushToDisk", "workerThreads", "proxyTimeout", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "externalDir", "disableOptimizedLoading", "gzipSize", "craCompat", "esmExternals", "fullySpecified", "outputFileTracingRoot", "NEXT_PRIVATE_OUTPUT_TRACE_ROOT", "swcTraceProfiling", "forceSwcTransforms", "swcPlugins", "largePageDataBytes", "disablePostcssPresetEnv", "urlImports", "adjustFontFallbacks", "adjustFontFallbacksWithSizeAdjust", "turbo", "turbotrace", "typedRoutes", "instrumentationHook", "bundlePagesExternals", "parallelServerCompiles", "parallelServerBuildTraces", "ppr", "__NEXT_TEST_MODE", "__NEXT_EXPERIMENTAL_PPR", "webpackBuildWorker", "missingSuspenseWithCSRBailout", "optimizeServerReact", "useEarlyImport", "staleTimes", "dynamic", "static", "normalizeConfig", "phase", "config"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAGnB,SAASC,kBAAkB,QAAQ,6BAA4B;AAqzB/D,OAAO,MAAMC,gBAA4B;IACvCC,KAAK,CAAC;IACNC,SAAS;IACTC,QAAQ;QACNC,oBAAoB;IACtB;IACAC,YAAY;QACVC,mBAAmB;QACnBC,cAAc;IAChB;IACAC,SAAS;IACTC,cAAc;IACdC,aAAa;IACbC,cAAcC;IACd,wBAAwB;IACxBC,oBAAoB,KAAK,OAAO;IAChCC,cAAc;IACdC,2BAA2B;IAC3BC,iBAAiB,IAAM;IACvBC,eAAe;IACfC,gBAAgB;QAAC;QAAO;QAAM;QAAO;KAAK;IAC1CC,iBAAiB;IACjBC,UAAU;IACVC,aAAaC,QAAQrB,GAAG,CAACsB,mBAAmB,IAAI;IAChDC,QAAQzB;IACR0B,eAAe;QACbC,eAAe;QACfC,uBAAuB;IACzB;IACAC,iBAAiB;QACfC,gBAAgB,KAAK;QACrBC,mBAAmB;IACrB;IACAC,KAAK;QACHC,eAAe;IACjB;IACAC,UAAU;IACVC,aAAa,CAAC;IACdC,eAAe;IACfC,MAAM;IACNC,6BAA6B;IAC7BC,eAAe;IACfC,6BAA6B;IAC7BC,qBAAqB,CAAC;IACtBC,qBAAqB,CAAC;IACtBC,0BAA0B;IAC1BC,iBAAiB;IACjBC,kBAAkB;QAChBC,WAAW;IACb;IACAC,mBAAmB;IACnBC,6BAA6B;IAC7BC,WAAW;IACXC,QAAQ,CAAC,CAAC3B,QAAQrB,GAAG,CAACiD,uBAAuB,GAAG,eAAetC;IAC/DuC,mBAAmBvC;IACnBwC,cAAc;QACZC,oBAAoB;QACpBC,oBAAoB;QACpBC,oBAAoB;QACpBC,kBAAkB;QAClBC,kBAAkB;QAClBC,qBAAqB;QACrBC,uBAAuB/C;QACvBgD,uBAAuBhD;QACvBiD,oBAAoB;QACpBC,6BAA6B;QAC7BC,qBAAqB;QACrBC,oBAAoB;QACpBC,uBAAuB;QACvBC,UAAUtD;QACVuD,sBAAsB;QACtBC,MAAMC,KAAKC,GAAG,CACZ,GACA,AAACC,CAAAA,OAAOjD,QAAQrB,GAAG,CAACuE,iBAAiB,KACnC,AAAC1E,CAAAA,GAAGsE,IAAI,MAAM;YAAEK,QAAQ;QAAE,CAAA,EAAGA,MAAM,AAAD,IAAK;QAE3CC,yBAAyB;QACzBC,gBAAgB;QAChBC,eAAe;QACfC,cAAcjE;QACdkE,aAAa;QACbC,mBAAmB;QACnBC,mBAAmB;QACnBC,aAAa;QACbC,yBAAyB;QACzBC,UAAU;QACVC,WAAW;QACXC,cAAc;QACdC,gBAAgB;QAChBC,uBAAuBjE,QAAQrB,GAAG,CAACuF,8BAA8B,IAAI;QACrEC,mBAAmB;QACnBC,oBAAoB;QACpBC,YAAY/E;QACZgF,oBAAoB,MAAM;QAC1BC,yBAAyBjF;QACzBmB,KAAKnB;QACLkF,YAAYlF;QACZmF,qBAAqB;QACrBC,mCAAmC;QACnCC,OAAOrF;QACPsF,YAAYtF;QACZuF,aAAa;QACbC,qBAAqB;QACrBC,sBAAsB;QACtBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,KACE,2CAA2C;QAC3C,2EAA2E;QAC3E,4EAA4E;QAC5E,4CAA4C;QAC5ClF,QAAQrB,GAAG,CAACwG,gBAAgB,IAC5BnF,QAAQrB,GAAG,CAACyG,uBAAuB,KAAK,SACpC,OACA;QACNC,oBAAoB/F;QACpBgG,+BAA+B;QAC/BC,qBAAqB;QACrBC,gBAAgB;QAChBC,YAAY;YACVC,SAAS;YACTC,QAAQ;QACV;IACF;AACF,EAAC;AAED,OAAO,eAAeC,gBAAgBC,KAAa,EAAEC,MAAW;IAC9D,IAAI,OAAOA,WAAW,YAAY;QAChCA,SAASA,OAAOD,OAAO;YAAEnH;QAAc;IACzC;IACA,gFAAgF;IAChF,OAAO,MAAMoH;AACf"}